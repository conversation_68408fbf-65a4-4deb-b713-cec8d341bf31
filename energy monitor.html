<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy Monitoring Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css" rel="stylesheet">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .value-display {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .unit {
            font-size: 1rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="text-center mb-4">Energy Monitoring Dashboard</h1>
        
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-header bg-primary text-white">
                        Voltage
                    </div>
                    <div class="card-body">
                        <div class="value-display" id="voltage">--</div>
                        <span class="unit">Volts</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-header bg-success text-white">
                        Current
                    </div>
                    <div class="card-body">
                        <div class="value-display" id="current">--</div>
                        <span class="unit">Amps</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-header bg-warning text-dark">
                        Power
                    </div>
                    <div class="card-body">
                        <div class="value-display" id="power">--</div>
                        <span class="unit">Watts</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-header bg-info text-white">
                        Power Factor
                    </div>
                    <div class="card-body">
                        <div class="value-display" id="powerFactor">--</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Energy Consumption
                    </div>
                    <div class="card-body">
                        <div class="value-display text-center" id="energy">--</div>
                        <span class="unit d-block text-center">kWh</span>
                        <canvas id="energyChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Real-time Monitoring
                    </div>
                    <div class="card-body">
                        <canvas id="realtimeChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Historical Data
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Voltage (V)</th>
                                    <th>Current (A)</th>
                                    <th>Power (W)</th>
                                    <th>Power Factor</th>
                                    <th>Energy (kWh)</th>
                                </tr>
                            </thead>
                            <tbody id="historyTable">
                                <!-- Data will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-database-compat.js"></script>
    <script src="code.js"></script>
</body>
</html>