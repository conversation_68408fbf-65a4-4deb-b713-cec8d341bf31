// Firebase configuration
const firebaseConfig = {

    apiKey: "AIzaSyAoaQoxcn5J-SN3YW1oBElWgZzrczMzkRg",

    authDomain: "energy-monitoring-system-514b8.firebaseapp.com",

    projectId: "energy-monitoring-system-514b8",

    storageBucket: "energy-monitoring-system-514b8.firebasestorage.app",

    messagingSenderId: "961579031396",

    appId: "1:961579031396:web:db2242af1357616302f871",

    measurementId: "G-XXJX5S4C0Z"

  };


// Initialize Firebase
const app = firebase.initializeApp(firebaseConfig);
const database = firebase.database();

// Chart variables
let realtimeChart, energyChart;
const maxDataPoints = 20; // Number of points to show in real-time chart
let voltageData = [];
let currentData = [];
let powerData = [];
let timestampData = [];

// Initialize charts
function initCharts() {
    // Real-time Chart
    const realtimeCtx = document.getElementById('realtimeChart').getContext('2d');
    realtimeChart = new Chart(realtimeCtx, {
        type: 'line',
        data: {
            labels: timestampData,
            datasets: [
                {
                    label: 'Voltage (V)',
                    data: voltageData,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y'
                },
                {
                    label: 'Current (A)',
                    data: currentData,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                },
                {
                    label: 'Power (W)',
                    data: powerData,
                    borderColor: 'rgba(255, 206, 86, 1)',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y2'
                }
            ]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Voltage (V)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'Current (A)'
                    }
                },
                y2: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'Power (W)'
                    }
                }
            }
        }
    });

    // Energy Chart
    const energyCtx = document.getElementById('energyChart').getContext('2d');
    energyChart = new Chart(energyCtx, {
        type: 'bar',
        data: {
            labels: ['Today', 'Week', 'Month'],
            datasets: [{
                label: 'Energy Consumption (kWh)',
                data: [0, 0, 0], // Placeholder, will be updated
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'kWh'
                    }
                }
            }
        }
    });
}

// Update display values
function updateDisplay(data) {
    document.getElementById('voltage').textContent = data.voltage.toFixed(2);
    document.getElementById('current').textContent = data.current.toFixed(2);
    document.getElementById('power').textContent = data.power.toFixed(2);
    document.getElementById('powerFactor').textContent = data.powerFactor.toFixed(2);
    document.getElementById('energy').textContent = data.energy.toFixed(3);
}

// Update real-time chart
function updateChart(data) {
    const now = new Date();
    const timeString = now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds();
    
    // Add new data
    timestampData.push(timeString);
    voltageData.push(data.voltage);
    currentData.push(data.current);
    powerData.push(data.power);
    
    // Limit data points
    if (timestampData.length > maxDataPoints) {
        timestampData.shift();
        voltageData.shift();
        currentData.shift();
        powerData.shift();
    }
    
    // Update chart
    realtimeChart.data.labels = timestampData;
    realtimeChart.data.datasets[0].data = voltageData;
    realtimeChart.data.datasets[1].data = currentData;
    realtimeChart.data.datasets[2].data = powerData;
    realtimeChart.update();
}

// Add data to history table
function addToHistory(data) {
    const now = new Date();
    const timeString = now.toLocaleString();
    
    const tableRow = document.createElement('tr');
    tableRow.innerHTML = `
        <td>${timeString}</td>
        <td>${data.voltage.toFixed(2)}</td>
        <td>${data.current.toFixed(2)}</td>
        <td>${data.power.toFixed(2)}</td>
        <td>${data.powerFactor.toFixed(2)}</td>
        <td>${data.energy.toFixed(3)}</td>
    `;
    
    const tableBody = document.getElementById('historyTable');
    tableBody.insertBefore(tableRow, tableBody.firstChild);
    
    // Limit history rows
    if (tableBody.children.length > 10) {
        tableBody.removeChild(tableBody.lastChild);
    }
}

// Listen for real-time data changes
function setupFirebaseListeners() {
    const dataRef = database.ref('data');
    
    dataRef.on('value', (snapshot) => {
        const data = snapshot.val();
        if (data) {
            updateDisplay(data);
            updateChart(data);
            addToHistory(data);
            
            // Update energy chart with some sample data (replace with actual historical data)
            energyChart.data.datasets[0].data = [
                data.energy,
                data.energy * 7,
                data.energy * 30
            ];
            energyChart.update();
        }
    });
}

// Initialize the application
function initApp() {
    initCharts();
    setupFirebaseListeners();
}

// Start the application when the page loads
window.addEventListener('load', initApp);